import { load } from "dotenv-mono";
import { createServiceClient } from "lib/supabase/service-client";
import { Client } from "pg";

load();

// Service client for bypassing RLS
export const serviceClient = createServiceClient();

// Database connection configuration (from postgrestools.jsonc)
const dbConfig = {
  host: "127.0.0.1",
  port: 54322,
  user: "postgres",
  password: "postgres",
  database: "postgres"
};

export const dbClient = new Client(dbConfig);
