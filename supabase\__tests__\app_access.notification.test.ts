import { afterAll, describe, expect, test } from "vitest";
import { mockCustomer, mockProvider } from "./mocks/auth.user";
import { serviceClient } from "./utils/client";
import { validateNotificationData } from "lib/schemas/notification";
import { findValidatedNotification } from "./utils/validation";

describe("Access Control Notification System", () => {
  const customer = mockCustomer();
  const provider = mockProvider();
  const createdNotificationIds: string[] = [];

  describe("Role Assignment Notifications", () => {
    test("role assigned notification is sent", async () => {
      if (!customer.data) throw new Error("Customer not defined");

      // Clear existing notifications first
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .eq("recipient_id", customer.data.id)
        .eq("type", "access.role.assigned");

      // Assign support_agent role to customer (less likely to already exist)
      await serviceClient.schema("app_access").rpc("assign_role_to_user", {
        v_user_id: customer.data.id,
        v_role_name: "support_agent"
      });

      // Check if notification was created
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "access.role.assigned");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "access.role.assigned",
        (data) => data.role_name === "support_agent"
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.role_name).toBe("support_agent");
      expect(validatedNotification!.data.assigned_at).toBeDefined();

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });

    test("role removed notification is sent", async () => {
      if (!customer.data) throw new Error("Customer not defined");

      // First assign a role
      await serviceClient.schema("app_access").rpc("assign_role_to_user", {
        v_user_id: customer.data.id,
        v_role_name: "support_agent"
      });

      // Clear existing notifications
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .eq("recipient_id", customer.data.id)
        .eq("type", "access.role.removed");

      // Remove the role
      await serviceClient.schema("app_access").rpc("revoke_role_from_user", {
        v_user_id: customer.data.id,
        v_role_name: "support_agent"
      });

      // Check if notification was created
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "access.role.removed");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "access.role.removed",
        (data) => data.role_name === "support_agent"
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.role_name).toBe("support_agent");
      expect(validatedNotification!.data.removed_at).toBeDefined();

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });

    test("multiple role assignments create separate notifications", async () => {
      if (!provider.data) throw new Error("Provider not defined");

      // Clear existing notifications first
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .eq("recipient_id", provider.data.id)
        .eq("type", "access.role.assigned");

      // Assign multiple roles
      await serviceClient.schema("app_access").rpc("assign_role_to_user", {
        v_user_id: provider.data.id,
        v_role_name: "support_agent"
      });

      await serviceClient.schema("app_access").rpc("assign_role_to_user", {
        v_user_id: provider.data.id,
        v_role_name: "celebrity"
      });

      // Check if notifications were created for both roles
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", provider.data.id)
        .eq("type", "access.role.assigned");

      expect(notifications).toBeDefined();

      // Check if we have role assignment notifications for the expected roles
      const roleNotifications = notifications!.filter((n) => {
        if (n.type !== "access.role.assigned") return false;
        const validatedData = validateNotificationData(
          "access.role.assigned",
          n.data
        );
        return ["support_agent", "celebrity"].includes(validatedData.role_name);
      });

      expect(roleNotifications.length).toBeGreaterThan(0);

      // Validate that at least one role notification exists
      const firstRoleNotification = roleNotifications[0];
      expect(firstRoleNotification).toBeDefined();

      const validatedData = validateNotificationData(
        "access.role.assigned",
        firstRoleNotification.data
      );
      expect(["support_agent", "celebrity"]).toContain(validatedData.role_name);

      // Add notification IDs for cleanup
      notifications!.forEach((notification) => {
        if (notification.id) {
          createdNotificationIds.push(notification.id);
        }
      });
    });

    test("no notification sent when role assignment fails", async () => {
      if (!customer.data) throw new Error("Customer not defined");

      // Try to assign a non-existent role (should fail)
      try {
        await serviceClient.schema("app_access").rpc("assign_role_to_user", {
          v_user_id: customer.data.id,
          v_role_name: "non_existent_role"
        });
      } catch {
        // Expected to fail
      }

      // Wait a moment for any potential notifications
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Check that no notification was created for the non-existent role
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "access.role.assigned");

      // Verify no notification exists for the non-existent role
      const invalidRoleNotification = notifications?.find((n) => {
        try {
          const data = JSON.parse(n.data as string);
          return data.role_name === "non_existent_role";
        } catch {
          return false;
        }
      });

      expect(invalidRoleNotification).toBeUndefined();
    });
  });

  afterAll(async () => {
    // Cleanup created notifications
    if (createdNotificationIds.length > 0) {
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .in("id", createdNotificationIds);
    }
  });
});
