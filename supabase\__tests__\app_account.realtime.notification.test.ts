/**
 * Account Realtime Notification Test - Notification Broadcasting
 *
 * This test demonstrates real-time notification broadcasting using Supabase Realtime.
 * It tests the ability to receive notifications in real-time when they are created.
 *
 * Key concepts tested:
 * - Creating notification channels for users
 * - Broadcasting notifications in real-time
 * - Handling notification channel connections with retry logic
 * - Cold start resilience for notification channels
 *
 * Sources:
 * - https://supabase.com/docs/guides/realtime/broadcast
 * - https://supabase.com/docs/guides/realtime/authorization
 */

import { afterAll, beforeAll, describe, expect, test } from "vitest";
import { mockCustomer, MockUser } from "./mocks/auth.user";
import { serviceClient } from "./utils/client";
import { RealtimeChannel } from "@supabase/supabase-js";
import { createRealtimeChannel } from "lib/supabase/realtime";
import type { Database } from "lib/supabase/database";

type NotificationRow = Database["app_account"]["Tables"]["notification"]["Row"];

interface NotificationData {
  health_check?: boolean;
  realtime?: boolean;
  order?: number;
  recovery_test?: boolean;
  [key: string]: unknown;
}

type NotificationChannelParams = {
  user: MockUser;
  receivedNotifications?: NotificationRow[];
};

async function createNotificationChannel({
  user,
  receivedNotifications = []
}: NotificationChannelParams): Promise<RealtimeChannel> {
  if (!user.client || !user.data)
    throw new Error("User client or data is undefined");

  const channelName = `notification:${user.data.id}`;
  const channelConfig = {
    config: { private: true, broadcast: { ack: true } }
  };

  // Create the channel with integrated health check
  const channel = await createRealtimeChannel({
    client: user.client,
    channelName,
    channelConfig,
    setupListeners: async (channel) => {
      // Set up the broadcast listener before health check runs
      channel.on(
        "broadcast",
        { event: "INSERT" },
        ({ payload }: { payload: NotificationRow }) => {
          receivedNotifications.push(payload);
        }
      );
    },
    healthCheck: {
      healthCheckFn: async () => {
        // Create a test notification to verify the channel is working
        await serviceClient.schema("app_account").rpc("create_notification", {
          p_type: "test.health_check",
          p_recipient_id: user.data!.id,
          p_title_key: "test.health_check.title",
          p_message_key: "test.health_check.message",
          p_data: { health_check: true }
        });
      },
      validateFn: () => {
        // Check if we received the health check notification
        return receivedNotifications.some(
          (n) =>
            n.type === "test.health_check" &&
            (n.data as NotificationData)?.health_check === true
        );
      },
      options: {
        maxAttempts: 30,
        checkDelay: 500,
        totalTimeout: 30000
      }
    },
    connectionOptions: {
      maxRetries: 3,
      baseDelay: 2000,
      maxDelay: 8000,
      connectionTimeout: 20000
    }
  });

  return channel;
}

describe("Account Realtime Notification Broadcasting", () => {
  const customer = mockCustomer();
  const createdNotificationIds: string[] = [];
  const receivedNotifications: NotificationRow[] = [];
  let notificationChannel: RealtimeChannel;

  beforeAll(async () => {
    if (!customer.data) throw new Error("Customer not defined");

    notificationChannel = await createNotificationChannel({
      user: customer,
      receivedNotifications
    });

    console.log("Notification channel setup complete");
  }, 60000);

  test("notifications are broadcast in realtime", async () => {
    if (!customer.data) throw new Error("Customer not defined");

    // Clear any existing notifications from health check

    // Create a notification
    const { data: notification } = await serviceClient
      .schema("app_account")
      .rpc("create_notification", {
        p_type: "test.realtime",
        p_recipient_id: customer.data.id,
        p_title_key: "test.realtime.title",
        p_message_key: "test.realtime.message",
        p_data: { realtime: true }
      });

    if (notification?.id) createdNotificationIds.push(notification.id);

    // Wait for the broadcast with timeout
    let attempts = 0;
    const maxAttempts = 20;
    let realtimeNotification: NotificationRow | undefined;

    while (attempts < maxAttempts && !realtimeNotification) {
      await new Promise((resolve) => setTimeout(resolve, 250));

      realtimeNotification = receivedNotifications.find(
        (n) => n.id === notification?.id && n.type === "test.realtime"
      );

      attempts++;
    }

    expect(realtimeNotification).toBeDefined();
    expect(realtimeNotification!.type).toBe("test.realtime");
    expect((realtimeNotification!.data as NotificationData).realtime).toBe(
      true
    );

    console.log(`Received notification after ${attempts * 250}ms`);
  });

  test("multiple notifications are received in order", async () => {
    if (!customer.data) throw new Error("Customer not defined");

    const testNotifications = [
      { type: "test.order.1", data: { order: 1 } },
      { type: "test.order.2", data: { order: 2 } },
      { type: "test.order.3", data: { order: 3 } }
    ];

    const initialCount = receivedNotifications.length;

    // Create multiple notifications quickly
    for (const notif of testNotifications) {
      const { data: notification } = await serviceClient
        .schema("app_account")
        .rpc("create_notification", {
          p_type: notif.type,
          p_recipient_id: customer.data.id,
          p_title_key: `${notif.type}.title`,
          p_message_key: `${notif.type}.message`,
          p_data: notif.data
        });

      if (notification?.id) createdNotificationIds.push(notification.id);
    }

    // Wait for all notifications to be received
    let attempts = 0;
    const maxAttempts = 40;

    while (attempts < maxAttempts) {
      const receivedCount = receivedNotifications.length - initialCount;
      if (receivedCount >= testNotifications.length) {
        break;
      }
      await new Promise((resolve) => setTimeout(resolve, 250));
      attempts++;
    }

    // Verify all notifications were received
    for (const notif of testNotifications) {
      const received = receivedNotifications.find((n) => n.type === notif.type);
      expect(received).toBeDefined();
      expect((received!.data as NotificationData).order).toBe(notif.data.order);
    }

    console.log(`Received ${testNotifications.length} notifications in order`);
  });

  test("channel handles connection interruption gracefully", async () => {
    if (!customer.data) throw new Error("Customer not defined");

    // Test that the channel can recover from simulated issues
    // by creating a notification after the channel has been established
    const { data: notification } = await serviceClient
      .schema("app_account")
      .rpc("create_notification", {
        p_type: "test.recovery",
        p_recipient_id: customer.data.id,
        p_title_key: "test.recovery.title",
        p_message_key: "test.recovery.message",
        p_data: { recovery_test: true }
      });

    if (notification?.id) createdNotificationIds.push(notification.id);

    // Wait for the notification
    let attempts = 0;
    const maxAttempts = 20;
    let recoveryNotification: NotificationRow | undefined;

    while (attempts < maxAttempts && !recoveryNotification) {
      await new Promise((resolve) => setTimeout(resolve, 250));

      recoveryNotification = receivedNotifications.find(
        (n) => n.id === notification?.id && n.type === "test.recovery"
      );

      attempts++;
    }

    expect(recoveryNotification).toBeDefined();
    expect((recoveryNotification!.data as NotificationData).recovery_test).toBe(
      true
    );
  });

  afterAll(async () => {
    // Cleanup created notifications
    if (createdNotificationIds.length > 0) {
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .in("id", createdNotificationIds);
    }

    // Unsubscribe from the channel
    if (notificationChannel) {
      await notificationChannel.unsubscribe();
    }
  });
});
