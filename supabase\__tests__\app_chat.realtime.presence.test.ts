/**
 * Chat Realtime Presence Test - Typing Indicators
 *
 * This test demonstrates how to implement "writing..." indicators in chat using Supabase Realtime Presence.
 *
 * Key concepts tested:
 * - Creating presence channels for chat conversations
 * - Tracking user typing state with presence.track()
 * - Simulating typing indicator workflows
 * - Handling multiple users typing simultaneously
 * - Implementing typing timeout concepts
 * - Authorization (users can only join conversations they're members of)
 *
 * Note: The actual presence sync between channels may not work perfectly in the test environment,
 * but the tests demonstrate the API usage and concepts for implementing typing indicators.
 *
 * Sources:
 * - https://supabase.com/docs/guides/realtime/presence?queryGroups=language&language=js
 * - https://supabase.com/docs/guides/realtime/authorization?queryGroups=language&language=js
 */

import { beforeAll, describe, expect, test } from "vitest";
import {
  MockConversation,
  mockConversation
} from "./mocks/app_chat.conversation";
import { mockCustomer, mockProvider, MockUser } from "./mocks/auth.user";
import { RealtimeChannel } from "@supabase/supabase-js";
import { createRealtimeChannel } from "lib/supabase/realtime";

type PresenceState = {
  user_id: string;
  typing: boolean;
  last_seen?: string;
  test_data?: string;
};

// Supabase presence state includes additional metadata
type SupabasePresenceState = PresenceState & {
  presence_ref: string;
};

// Type definitions for Supabase presence events
type PresenceJoinEvent = {
  key: string;
  newPresences: SupabasePresenceState[];
};

type PresenceLeaveEvent = {
  key: string;
  leftPresences: SupabasePresenceState[];
};

type ChannelParams = {
  user: MockUser;
  conversation: MockConversation;
  presenceStates?: Record<string, SupabasePresenceState[]>;
  presenceJoins?: SupabasePresenceState[];
  presenceLeaves?: SupabasePresenceState[];
};

async function createPresenceChannel({
  user,
  conversation,
  presenceStates = {},
  presenceJoins = [],
  presenceLeaves = []
}: ChannelParams): Promise<RealtimeChannel> {
  if (!user.client) throw new Error("User client is undefined");
  if (!user.data) throw new Error("User data is undefined");
  if (!conversation.id) throw new Error("Conversation ID is undefined");

  const channelName = `chat:${conversation.id}`;
  const channelConfig = {
    config: { private: true }
  };

  // Create channel with integrated health check
  const channel = await createRealtimeChannel({
    client: user.client,
    channelName,
    channelConfig,
    setupListeners: async (channel) => {
      // Set up event listeners before health check runs
      channel
        .on("presence", { event: "sync" }, () => {
          const state = channel.presenceState();
          console.log("Presence sync:", state);
          Object.assign(presenceStates, state);
        })
        .on(
          "presence",
          { event: "join" },
          ({ key, newPresences }: PresenceJoinEvent) => {
            console.log("User joined:", key, newPresences);
            presenceJoins.push(...newPresences);
          }
        )
        .on(
          "presence",
          { event: "leave" },
          ({ key, leftPresences }: PresenceLeaveEvent) => {
            console.log("User left:", key, leftPresences);
            presenceLeaves.push(...leftPresences);
          }
        );
    },
    healthCheck: {
      healthCheckFn: async (channel) => {
        // Perform a simple health check - track presence
        await channel.track({
          user_id: user.data!.id,
          typing: false,
          last_seen: new Date().toISOString()
        });
      },
      validateFn: (channel) => {
        // Validate that presence state was established
        const state = channel.presenceState();
        return Object.keys(state).length > 0;
      },
      options: {
        maxAttempts: 10,
        checkDelay: 500,
        totalTimeout: 15000
      }
    },
    connectionOptions: {
      maxRetries: 3,
      baseDelay: 2000,
      maxDelay: 8000,
      connectionTimeout: 15000
    }
  });

  return channel;
}

const customer = mockCustomer();
const provider = mockProvider();

describe("chat presence - typing indicators", () => {
  const conversation = mockConversation({
    members: [customer, provider]
  });

  let customerChannel: RealtimeChannel;
  let providerChannel: RealtimeChannel;

  const customerPresenceStates: Record<string, SupabasePresenceState[]> = {};
  const providerPresenceStates: Record<string, SupabasePresenceState[]> = {};
  const customerPresenceJoins: SupabasePresenceState[] = [];
  const providerPresenceJoins: SupabasePresenceState[] = [];
  const customerPresenceLeaves: SupabasePresenceState[] = [];
  const providerPresenceLeaves: SupabasePresenceState[] = [];

  // Note: Removed manual typingStates tracking - now testing real presence behavior

  beforeAll(async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    customerChannel = await createPresenceChannel({
      user: customer,
      conversation,
      presenceStates: customerPresenceStates,
      presenceJoins: customerPresenceJoins,
      presenceLeaves: customerPresenceLeaves
    });

    providerChannel = await createPresenceChannel({
      user: provider,
      conversation,
      presenceStates: providerPresenceStates,
      presenceJoins: providerPresenceJoins,
      presenceLeaves: providerPresenceLeaves
    });

    console.log("Both chat presence channels connected successfully");
    console.log("Customer presence states:", customerPresenceStates);
    console.log("Provider presence states:", providerPresenceStates);
  }, 60000);

  test("can create presence channels for conversation members", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    // Verify channels were created successfully
    expect(customerChannel).toBeDefined();
    expect(providerChannel).toBeDefined();

    // Test that we can call track on the channels (basic functionality)
    const trackResult1 = await customerChannel.track({
      user_id: customer.data.id,
      typing: false,
      last_seen: new Date().toISOString()
    });

    const trackResult2 = await providerChannel.track({
      user_id: provider.data.id,
      typing: false,
      last_seen: new Date().toISOString()
    });

    // Track calls should not throw errors (may return 'ok' or 'error' depending on setup)
    expect(trackResult1).toBeDefined();
    expect(trackResult2).toBeDefined();
  });

  test("investigate track() behavior and RLS policy enforcement", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    console.log("=== INVESTIGATING TRACK() BEHAVIOR ===");

    // Test 1: Check if track() calls succeed or fail
    try {
      console.log("Testing customer track() call...");
      const customerTrackResult = await customerChannel.track({
        user_id: customer.data.id,
        typing: true,
        last_seen: new Date().toISOString(),
        test_data: "investigating_behavior"
      });

      console.log("✅ Customer track() succeeded:", customerTrackResult);
      expect(customerTrackResult).toBeDefined();
    } catch (error) {
      console.log("❌ Customer track() failed:", error);
      throw error;
    }

    console.log("=== BASIC TRACK TEST COMPLETE ===");
  }, 30000);

  test("can simulate typing indicator workflow with real cross-client presence", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    let providerReceivedCustomerTyping = false;
    let providerReceivedCustomerStoppedTyping = false;

    // Set up provider to listen for customer's presence changes BEFORE any tracking
    const providerPresencePromise = new Promise<void>((resolve) => {
      let hasSeenTyping = false;

      providerChannel.on("presence", { event: "sync" }, () => {
        const state = providerChannel.presenceState();

        // Search for customer presence by user_id (not by presence key)
        Object.values(state).forEach((presences) => {
          const presence = presences[0] as SupabasePresenceState;
          if (presence?.user_id === customer.data?.id) {
            if (presence.typing === true && !hasSeenTyping) {
              hasSeenTyping = true;
              providerReceivedCustomerTyping = true;
            } else if (presence.typing === false && hasSeenTyping) {
              providerReceivedCustomerStoppedTyping = true;
              resolve();
            }
          }
        });
      });
    });

    // Wait a moment for event listeners to be set up
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Customer starts typing
    console.log("Customer starting to type...");
    const startTypingResult = await customerChannel.track({
      user_id: customer.data.id,
      typing: true,
      last_seen: new Date().toISOString()
    });

    expect(startTypingResult).toBeDefined();
    console.log("Customer track result:", startTypingResult);

    // Wait for presence sync
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Verify provider received customer's typing state
    expect(providerReceivedCustomerTyping).toBe(true);

    // Customer stops typing
    console.log("Customer stopping typing...");
    const stopTypingResult = await customerChannel.track({
      user_id: customer.data.id,
      typing: false,
      last_seen: new Date().toISOString()
    });

    expect(stopTypingResult).toBeDefined();

    // Wait for the provider to receive the stop typing event
    await Promise.race([
      providerPresencePromise,
      new Promise((_, reject) =>
        setTimeout(
          () => reject(new Error("Timeout waiting for stop typing event")),
          3000
        )
      )
    ]);

    // Verify provider received customer's stopped typing state
    expect(providerReceivedCustomerStoppedTyping).toBe(true);

    // Verify final presence state
    const finalProviderState =
      providerChannel.presenceState<SupabasePresenceState>();
    const customerPresenceInFinalState = Object.values(finalProviderState).find(
      (presences) => presences[0]?.user_id === customer.data?.id
    );
    expect(customerPresenceInFinalState).toBeDefined();
    expect(customerPresenceInFinalState![0]?.typing).toBe(false);
  });

  test("can handle multiple users typing simultaneously with real presence", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    let customerSeesProviderTyping = false;
    let providerSeesCustomerTyping = false;
    let bothUsersStoppedTyping = false;

    // Set up cross-client presence listeners BEFORE any tracking
    const crossClientPromise = new Promise<void>((resolve) => {
      let customerTypingDetected = false;
      let providerTypingDetected = false;
      let bothStoppedDetected = false;

      customerChannel.on("presence", { event: "sync" }, () => {
        const state = customerChannel.presenceState<SupabasePresenceState>();

        // Search for provider presence by user_id
        Object.values(state).forEach((presences) => {
          const presence = presences[0];
          if (presence?.user_id === provider.data?.id) {
            if (presence.typing === true && !customerSeesProviderTyping) {
              customerSeesProviderTyping = true;
              customerTypingDetected = true;
            }
          }
        });

        // Check if both stopped typing by searching for both users
        let customerPresence: SupabasePresenceState | null = null;
        let providerPresence: SupabasePresenceState | null = null;

        Object.values(state).forEach((presences) => {
          const presence = presences[0];
          if (presence?.user_id === customer.data?.id) {
            customerPresence = presence;
          } else if (presence?.user_id === provider.data?.id) {
            providerPresence = presence;
          }
        });

        if (
          customerPresence &&
          providerPresence &&
          (customerPresence as SupabasePresenceState).typing === false &&
          (providerPresence as SupabasePresenceState).typing === false &&
          !bothStoppedDetected
        ) {
          bothUsersStoppedTyping = true;
          bothStoppedDetected = true;

          if (customerTypingDetected && providerTypingDetected) {
            resolve();
          }
        }
      });

      providerChannel.on("presence", { event: "sync" }, () => {
        const state = providerChannel.presenceState<SupabasePresenceState>();

        // Search for customer presence by user_id
        Object.values(state).forEach((presences) => {
          const presence = presences[0];
          if (presence?.user_id === customer.data?.id) {
            if (presence.typing === true && !providerSeesCustomerTyping) {
              providerSeesCustomerTyping = true;
              providerTypingDetected = true;
              console.log("✅ Provider detected customer typing");
            }
          }
        });
      });
    });

    // Wait for event listeners to be set up
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Both users start typing
    const customerTypingResult = await customerChannel.track({
      user_id: customer.data.id,
      typing: true,
      last_seen: new Date().toISOString()
    });

    const providerTypingResult = await providerChannel.track({
      user_id: provider.data.id,
      typing: true,
      last_seen: new Date().toISOString()
    });

    expect(customerTypingResult).toBeDefined();
    expect(providerTypingResult).toBeDefined();

    // Wait for presence sync
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Verify cross-client presence updates
    expect(customerSeesProviderTyping).toBe(true);
    expect(providerSeesCustomerTyping).toBe(true);

    // Both stop typing
    await customerChannel.track({
      user_id: customer.data.id,
      typing: false,
      last_seen: new Date().toISOString()
    });

    await providerChannel.track({
      user_id: provider.data.id,
      typing: false,
      last_seen: new Date().toISOString()
    });

    // Wait for both users to stop typing
    await Promise.race([
      crossClientPromise,
      new Promise((_, reject) =>
        setTimeout(
          () =>
            reject(new Error("Timeout waiting for both users to stop typing")),
          5000
        )
      )
    ]);

    // Verify both users stopped typing
    expect(bothUsersStoppedTyping).toBe(true);

    // Verify final presence states
    const customerFinalState =
      customerChannel.presenceState<SupabasePresenceState>();
    const providerFinalState =
      providerChannel.presenceState<SupabasePresenceState>();

    const providerPresenceInCustomerState = Object.values(
      customerFinalState
    ).find((presences) => presences[0]?.user_id === provider.data?.id);
    const customerPresenceInProviderState = Object.values(
      providerFinalState
    ).find((presences) => presences[0]?.user_id === customer.data?.id);

    expect(providerPresenceInCustomerState).toBeDefined();
    expect(customerPresenceInProviderState).toBeDefined();
    expect(providerPresenceInCustomerState![0]?.typing).toBe(false);
    expect(customerPresenceInProviderState![0]?.typing).toBe(false);
  }, 30000);

  test("demonstrates typing indicator timeout concept with real presence", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");

    let customerStartedTyping = false;
    let customerStoppedTyping = false;
    const startTime = new Date();

    // Set up provider to listen for customer's presence changes BEFORE tracking
    const timeoutPromise = new Promise<void>((resolve) => {
      providerChannel.on("presence", { event: "sync" }, () => {
        const state = providerChannel.presenceState<SupabasePresenceState>();
        // Search for customer presence by user_id
        Object.values(state).forEach((presences) => {
          const presence = presences[0];
          if (presence?.user_id === customer.data?.id) {
            if (presence.typing === true && !customerStartedTyping) {
              customerStartedTyping = true;
            } else if (presence.typing === false && customerStartedTyping) {
              customerStoppedTyping = true;
              resolve();
            }
          }
        });
      });
    });

    // Wait for event listeners to be set up
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Simulate user starting to type
    const startTypingResult = await customerChannel.track({
      user_id: customer.data.id,
      typing: true,
      last_seen: startTime.toISOString()
    });

    expect(startTypingResult).toBeDefined();

    // Wait for presence sync
    await new Promise((resolve) => setTimeout(resolve, 1000));
    expect(customerStartedTyping).toBe(true);

    // Simulate a timeout scenario where user stops typing
    // In a real app, you'd have a timeout that automatically sets typing: false after inactivity
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Simulate timeout clearing the typing indicator (like after 3 seconds of inactivity)
    const now = new Date();

    // Clear typing indicator due to timeout
    await customerChannel.track({
      user_id: customer.data.id,
      typing: false,
      last_seen: now.toISOString()
    });

    // Wait for the provider to receive the stop typing event
    await Promise.race([
      timeoutPromise,
      new Promise((_, reject) =>
        setTimeout(
          () => reject(new Error("Timeout waiting for stop typing event")),
          3000
        )
      )
    ]);

    // Verify provider received the stop typing update
    expect(customerStoppedTyping).toBe(true);

    // Verify final presence state shows not typing
    const finalState = providerChannel.presenceState<SupabasePresenceState>();
    const customerPresenceInFinalState = Object.values(finalState).find(
      (presences) => presences[0]?.user_id === customer.data?.id
    );
    expect(customerPresenceInFinalState).toBeDefined();
    expect(customerPresenceInFinalState![0]?.typing).toBe(false);
  });

  const customer2 = mockCustomer();
  const conversation2 = mockConversation({
    members: [customer2, provider]
  });

  test("cannot connect to presence channel for conversation they are not part of", async () => {
    await expect(
      createPresenceChannel({
        user: customer,
        conversation: conversation2,
        presenceStates: {},
        presenceJoins: [],
        presenceLeaves: []
      })
    ).rejects.toThrow();
  });
});
