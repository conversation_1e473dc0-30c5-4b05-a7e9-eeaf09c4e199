import React from "react";
import { <PERSON>, CardContent, Typography, Box, Chip, Alert } from "@mui/material";
import { usePermissions } from "../hooks/usePermissions";
import { PermissionGuard } from "./PermissionGuard";

/**
 * Demo component showing how to use type-safe permissions
 */
export function PermissionDemo() {
  const {
    permissions,
    isLoading,
    hasRole,
    hasCapability,
    isAdmin,
    canRead,
    canWrite,
    canDelete,
  } = usePermissions();

  if (isLoading) {
    return <Typography>Loading permissions...</Typography>;
  }

  if (!permissions) {
    return <Alert severity="warning">No permissions found</Alert>;
  }

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" gutterBottom>
        Permission System Demo
      </Typography>

      {/* Current User Permissions */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Current User Permissions
          </Typography>
          
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2">Roles:</Typography>
            {permissions.roles.map((role) => (
              <Chip key={role} label={role} color="primary" sx={{ mr: 1, mb: 1 }} />
            ))}
          </Box>

          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2">Capabilities:</Typography>
            {permissions.capabilities.map((capability) => (
              <Chip key={capability} label={capability} color="secondary" sx={{ mr: 1, mb: 1 }} />
            ))}
          </Box>

          <Typography variant="subtitle2">
            Admin Status: {isAdmin() ? "✅ Admin" : "❌ Not Admin"}
          </Typography>
        </CardContent>
      </Card>

      {/* Role-based Access Examples */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Role-based Access Examples
          </Typography>

          <PermissionGuard role="admin">
            <Alert severity="success" sx={{ mb: 1 }}>
              ✅ You have admin role - this content is visible
            </Alert>
          </PermissionGuard>

          <PermissionGuard role="moderator">
            <Alert severity="info" sx={{ mb: 1 }}>
              ✅ You have moderator role - this content is visible
            </Alert>
          </PermissionGuard>

          <PermissionGuard role="provider">
            <Alert severity="info" sx={{ mb: 1 }}>
              ✅ You have provider role - this content is visible
            </Alert>
          </PermissionGuard>

          <PermissionGuard role="customer">
            <Alert severity="info" sx={{ mb: 1 }}>
              ✅ You have customer role - this content is visible
            </Alert>
          </PermissionGuard>

          <PermissionGuard roles={["admin", "moderator"]}>
            <Alert severity="warning" sx={{ mb: 1 }}>
              ✅ You have admin OR moderator role - this content is visible
            </Alert>
          </PermissionGuard>
        </CardContent>
      </Card>

      {/* Capability-based Access Examples */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Capability-based Access Examples
          </Typography>

          <PermissionGuard capability="user:read">
            <Alert severity="success" sx={{ mb: 1 }}>
              ✅ You can read users - this content is visible
            </Alert>
          </PermissionGuard>

          <PermissionGuard capability="user:write">
            <Alert severity="warning" sx={{ mb: 1 }}>
              ✅ You can write users - this content is visible
            </Alert>
          </PermissionGuard>

          <PermissionGuard capability="order:read">
            <Alert severity="info" sx={{ mb: 1 }}>
              ✅ You can read orders - this content is visible
            </Alert>
          </PermissionGuard>

          <PermissionGuard capability="system:admin">
            <Alert severity="error" sx={{ mb: 1 }}>
              ✅ You have system admin capability - this content is visible
            </Alert>
          </PermissionGuard>
        </CardContent>
      </Card>

      {/* Resource-based Access Examples */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Resource-based Access Examples
          </Typography>

          <Box sx={{ mb: 1 }}>
            <Typography variant="body2">
              User Resource: 
              {canRead("user") && " ✅ Read"}
              {canWrite("user") && " ✅ Write"}
              {canDelete("user") && " ✅ Delete"}
            </Typography>
          </Box>

          <Box sx={{ mb: 1 }}>
            <Typography variant="body2">
              Order Resource: 
              {canRead("order") && " ✅ Read"}
              {canWrite("order") && " ✅ Write"}
              {canDelete("order") && " ✅ Delete"}
            </Typography>
          </Box>

          <Box sx={{ mb: 1 }}>
            <Typography variant="body2">
              Ticket Resource: 
              {canRead("ticket") && " ✅ Read"}
              {canWrite("ticket") && " ✅ Write"}
              {canDelete("ticket") && " ✅ Delete"}
            </Typography>
          </Box>

          <PermissionGuard resource="user" action="write">
            <Alert severity="success" sx={{ mb: 1 }}>
              ✅ You can write to user resource - this content is visible
            </Alert>
          </PermissionGuard>

          <PermissionGuard resource="order" action="delete">
            <Alert severity="error" sx={{ mb: 1 }}>
              ✅ You can delete orders - this content is visible
            </Alert>
          </PermissionGuard>
        </CardContent>
      </Card>

      {/* Admin-only Content */}
      <PermissionGuard adminOnly>
        <Card sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom color="error">
              🔒 Admin-only Content
            </Typography>
            <Alert severity="error">
              This content is only visible to administrators. You have admin access!
            </Alert>
          </CardContent>
        </Card>
      </PermissionGuard>

      {/* Fallback Example */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Fallback Example
          </Typography>
          <PermissionGuard 
            capability="non-existent-capability" 
            fallback={
              <Alert severity="warning">
                ❌ You don't have the required capability - showing fallback content
              </Alert>
            }
          >
            <Alert severity="success">
              ✅ You have the required capability - showing main content
            </Alert>
          </PermissionGuard>
        </CardContent>
      </Card>
    </Box>
  );
}
