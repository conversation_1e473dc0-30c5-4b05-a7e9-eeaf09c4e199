import { usePermissions as useReactAdminPermissions } from "react-admin";
import type { UserPermissions, UserRole, UserCapability } from "../providers/authProvider";

/**
 * Hook to access user permissions with type safety
 */
export function usePermissions() {
  const { permissions, isLoading } = useReactAdminPermissions<UserPermissions>();

  /**
   * Check if user has a specific role
   */
  const hasRole = (role: UserRole): boolean => {
    return permissions?.roles?.includes(role) ?? false;
  };

  /**
   * Check if user has any of the specified roles
   */
  const hasAnyRole = (roles: UserRole[]): boolean => {
    return roles.some(role => hasRole(role));
  };

  /**
   * Check if user has all of the specified roles
   */
  const hasAllRoles = (roles: UserRole[]): boolean => {
    return roles.every(role => hasRole(role));
  };

  /**
   * Check if user has a specific capability
   */
  const hasCapability = (capability: UserCapability): boolean => {
    return permissions?.capabilities?.includes(capability) ?? false;
  };

  /**
   * Check if user has any of the specified capabilities
   */
  const hasAnyCapability = (capabilities: UserCapability[]): boolean => {
    return capabilities.some(capability => hasCapability(capability));
  };

  /**
   * Check if user has all of the specified capabilities
   */
  const hasAllCapabilities = (capabilities: UserCapability[]): boolean => {
    return capabilities.every(capability => hasCapability(capability));
  };

  /**
   * Check if user is admin (has admin role or system:admin capability)
   */
  const isAdmin = (): boolean => {
    return hasRole("admin") || hasCapability("system:admin");
  };

  /**
   * Check if user can perform CRUD operations on a resource
   */
  const canRead = (resource: string): boolean => {
    return hasCapability(`${resource}:read` as UserCapability);
  };

  const canWrite = (resource: string): boolean => {
    return hasCapability(`${resource}:write` as UserCapability);
  };

  const canDelete = (resource: string): boolean => {
    return hasCapability(`${resource}:delete` as UserCapability);
  };

  return {
    permissions,
    isLoading,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    hasCapability,
    hasAnyCapability,
    hasAllCapabilities,
    isAdmin,
    canRead,
    canWrite,
    canDelete,
  };
}
