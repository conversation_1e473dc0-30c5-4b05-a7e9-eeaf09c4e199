# React Admin Dashboard

This dashboard uses React Admin with proper permission handling via the `canAccess` method.

## Permission System

### Schema Location

- User data schemas are located in `lib/schemas/auth.ts`
- Single source of truth for all permission-related types

### How Permissions Work

1. **Database Integration**: Permissions are loaded from the `user_roles_and_capabilities` view
2. **canAccess Method**: React Admin automatically calls `authProvider.canAccess({ action, resource })`
3. **Dynamic Permissions**: All roles and capabilities come from the database

### Permission Logic

The `canAccess` method checks permissions in this order:

1. **Admin Override**: Users with `admin` role or `system:admin` capability have full access
2. **Capability Check**: Checks for specific `resource:action` capability (e.g., `users:list`, `orders:edit`)

### Usage in Components

React Admin automatically handles permissions for:

- **Resource visibility**: Resources are hidden if user lacks `resource:list` capability
- **Action buttons**: Edit/Create/Delete buttons are hidden based on `resource:edit`, `resource:create`, `resource:delete`
- **Menu items**: Navigation items are filtered based on permissions

### Custom Permission Checks

Use the `usePermissions` hook for custom permission logic:

```typescript
import { usePermissions } from "./hooks/usePermissions";

function MyComponent() {
  const { hasRole, hasCapability, isAdmin } = usePermissions();

  if (isAdmin()) {
    return <AdminPanel />;
  }

  if (hasCapability("users:write")) {
    return <EditUserButton />;
  }

  return <ReadOnlyView />;
}
```

### Adding New Permissions

1. Add roles/capabilities to your database
2. The `user_roles_and_capabilities` view will automatically include them
3. No code changes needed - permissions are dynamic

## Resources

Current resources with permission requirements:

- **test**: No permissions required (visible to all authenticated users)
- **users**: Requires `users:list`, `users:edit`, `users:create`, `users:delete`
- **orders**: Requires `orders:list`, `orders:edit`, `orders:create`, `orders:delete`
- **tickets**: Requires `tickets:list`, `tickets:edit`, `tickets:create`, `tickets:delete`
- **providers**: Requires `providers:list`, `providers:edit`, `providers:create`, `providers:delete`
- **activities**: Requires `activities:list`, `activities:edit`, `activities:create`, `activities:delete`

## Installation

Install the application dependencies by running:

```sh
npm install
# or
yarn install
# or
bun install
```

## Development

Start the application in development mode by running:

```sh
npm run dev
# or
yarn dev
# orun run dev
```

## Production

Build the application in production mode by running:

```sh
npm run build
# or
yarn build
# or
bun run build
```
