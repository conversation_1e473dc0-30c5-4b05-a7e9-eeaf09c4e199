import type { Auth<PERSON>rovider } from "react-admin";
import { createClient } from "lib/supabase/client";
import { tryCatch } from "lib/utils/trycatch";
import {
  UserDataSchema,
  UserPermissionsSchema,
  type UserData,
  type UserIdentity,
} from "lib/schemas/auth";

// Helper function to safely parse user data from database view
function parseUserData(
  authUser: { id: string; email?: string | null },
  userRolesData:
    | { roles?: string[] | null; capabilities?: string[] | null }
    | null
    | undefined,
): UserData {
  const rawUserData = {
    id: authUser.id,
    email: authUser.email || "",
    roles: userRolesData?.roles || [],
    capabilities: userRolesData?.capabilities || [],
  };

  // Use synchronous parsing with manual error handling since tryCatch is async
  const parseResult = UserDataSchema.safeParse(rawUserData);

  if (!parseResult.success) {
    console.warn(
      "Failed to parse user data, using defaults:",
      parseResult.error,
    );
    // Return safe defaults if parsing fails
    return {
      id: authUser.id,
      email: authUser.email || "",
      roles: [],
      capabilities: [],
    };
  }

  return parseResult.data;
}

// Storage keys for authentication state
const AUTH_STORAGE_KEY = "auth_user";
const PERMISSIONS_STORAGE_KEY = "auth_permissions";

// Create a singleton Supabase client to avoid multiple instances
const supabaseClient = createClient();

export const authProvider: AuthProvider = {
  // Login with email and password
  async login(params: { email?: string; username?: string; password: string }) {
    try {
      // React Admin's default login form uses 'username', but we need 'email'
      const email = params.email || params.username;
      const { password } = params;

      console.log("Attempting login with:", {
        email,
        password: password ? "***" : "missing",
        supabaseUrl: process.env.SUPABASE_URL,
        hasAnonKey: !!process.env.SUPABASE_ANON_KEY,
        receivedParams: Object.keys(params),
      });

      if (!email || !password) {
        throw new Error("Email and password are required");
      }

      // Sign in with email and password
      console.log("Calling Supabase signInWithPassword...");
      const { data: authData, error: authError } =
        await supabaseClient.auth.signInWithPassword({
          email,
          password,
        });

      console.log("Supabase response:", {
        hasUser: !!authData?.user,
        userId: authData?.user?.id,
        error: authError,
      });

      if (authError) {
        console.error("Supabase auth error:", authError);
        throw new Error(authError.message);
      }

      if (!authData.user) {
        throw new Error("Authentication failed");
      }

      console.log("Authentication successful for user:", authData.user.id);

      // Fetch user roles and capabilities
      const { data: userRolesData, error: rolesError } = await supabaseClient
        .schema("app_access")
        .from("user_roles_and_capabilities")
        .select("*")
        .eq("user_id", authData.user.id)
        .single();

      if (rolesError && rolesError.code !== "PGRST116") {
        // PGRST116 = no rows returned
        console.warn("Failed to fetch user roles:", rolesError.message);
      }

      // Prepare user data with validation
      const userData = parseUserData(authData.user, userRolesData);

      // Store user data and permissions in localStorage
      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(userData));
      localStorage.setItem(
        PERMISSIONS_STORAGE_KEY,
        JSON.stringify({
          roles: userData.roles,
          capabilities: userData.capabilities,
        }),
      );

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  },

  // Logout user
  async logout() {
    try {
      await supabaseClient.auth.signOut();
    } catch (error) {
      console.warn("Error during logout:", error);
    }

    // Clear stored data
    localStorage.removeItem(AUTH_STORAGE_KEY);
    localStorage.removeItem(PERMISSIONS_STORAGE_KEY);

    return Promise.resolve();
  },

  // Check if user is authenticated
  async checkAuth() {
    try {
      const {
        data: { user },
        error,
      } = await supabaseClient.auth.getUser();

      if (error || !user) {
        // Clear any stale data
        localStorage.removeItem(AUTH_STORAGE_KEY);
        localStorage.removeItem(PERMISSIONS_STORAGE_KEY);
        throw new Error("Not authenticated");
      }

      // Check if we have stored user data
      const storedUserData = localStorage.getItem(AUTH_STORAGE_KEY);
      if (!storedUserData) {
        // If no stored data, fetch it
        const { data: userRolesData, error: rolesError } = await supabaseClient
          .schema("app_access")
          .from("user_roles_and_capabilities")
          .select("*")
          .eq("user_id", user.id)
          .single();

        if (rolesError && rolesError.code !== "PGRST116") {
          console.warn("Failed to fetch user roles:", rolesError.message);
        }

        const userData = parseUserData(user, userRolesData);

        localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(userData));
        localStorage.setItem(
          PERMISSIONS_STORAGE_KEY,
          JSON.stringify({
            roles: userData.roles,
            capabilities: userData.capabilities,
          }),
        );
      }

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  },

  // Check if an error is an authentication error
  async checkError(error: { status?: number; message?: string }) {
    const status = error.status;

    if (status === 401 || status === 403) {
      // Clear stored data on auth errors
      localStorage.removeItem(AUTH_STORAGE_KEY);
      localStorage.removeItem(PERMISSIONS_STORAGE_KEY);
      throw new Error("Session expired");
    }

    // For other errors, don't redirect to login
    return Promise.resolve();
  },

  // Get user identity
  async getIdentity() {
    const storedUserData = localStorage.getItem(AUTH_STORAGE_KEY);

    if (!storedUserData) {
      throw new Error("No user data found");
    }

    const [parseError, rawUserData] = await tryCatch(() =>
      JSON.parse(storedUserData),
    );
    if (parseError) {
      console.warn("Failed to parse stored user data:", parseError);
      throw new Error("Invalid user data");
    }

    const userDataResult = UserDataSchema.safeParse(rawUserData);
    if (!userDataResult.success) {
      console.warn(
        "Failed to validate stored user data:",
        userDataResult.error,
      );
      throw new Error("Invalid user data");
    }

    const identity: UserIdentity = {
      id: userDataResult.data.id,
      fullName: userDataResult.data.email,
      avatar: undefined,
    };

    return Promise.resolve(identity);
  },

  // Get user permissions (roles and capabilities)
  async getPermissions() {
    const storedPermissions = localStorage.getItem(PERMISSIONS_STORAGE_KEY);

    if (!storedPermissions) {
      return Promise.resolve(undefined);
    }

    const [parseError, rawPermissions] = await tryCatch(() =>
      JSON.parse(storedPermissions),
    );
    if (parseError) {
      console.warn("Failed to parse stored permissions:", parseError);
      return Promise.resolve(undefined);
    }

    const permissionsResult = UserPermissionsSchema.safeParse(rawPermissions);
    if (!permissionsResult.success) {
      console.warn(
        "Failed to validate stored permissions:",
        permissionsResult.error,
      );
      return Promise.resolve(undefined);
    }

    return Promise.resolve(permissionsResult.data);
  },
  canAccess: async ({ action, resource }) => {
    return true;

    const storedPermissions = localStorage.getItem(PERMISSIONS_STORAGE_KEY);

    if (!storedPermissions) {
      return false;
    }

    const [parseError, rawPermissions] = await tryCatch(() =>
      JSON.parse(storedPermissions),
    );
    if (parseError) {
      console.warn("Failed to parse stored permissions:", parseError);
      return false;
    }

    const permissionsResult = UserPermissionsSchema.safeParse(rawPermissions);
    if (!permissionsResult.success) {
      console.warn(
        "Failed to validate stored permissions:",
        permissionsResult.error,
      );
      return false;
    }

    const permissions = permissionsResult.data;

    // Check if user has admin role or system:admin capability
    if (permissions.roles.includes("admin")) {
      return true;
    }

    // Check specific capability for resource:action
    const requiredCapability = `${resource}:${action}`;
    return permissions.capabilities.includes(requiredCapability);
  },
};
