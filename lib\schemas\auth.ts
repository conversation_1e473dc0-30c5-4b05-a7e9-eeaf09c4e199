import { z } from "zod";

export const UserDataSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  roles: z.array(z.string()),
  capabilities: z.array(z.string())
});

export const UserPermissionsSchema = z.object({
  roles: z.array(z.string()),
  capabilities: z.array(z.string())
});

export const UserIdentitySchema = z.object({
  id: z.string().uuid(),
  fullName: z.string(),
  avatar: z.string().url().optional()
});

export type UserData = z.infer<typeof UserDataSchema>;
export type UserPermissions = z.infer<typeof UserPermissionsSchema>;
export type UserIdentity = z.infer<typeof UserIdentitySchema>;
