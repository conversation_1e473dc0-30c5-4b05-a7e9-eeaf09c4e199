---
type: "always_apply"
---

# Commands

## Running SQL Files

After migration file (.sql) changes, always run `pnpm supabase:full-reset`. If that's successful run `pnpm check:supabase` to test if the types and coding style is correct.

Do not run `supabase db reset --local`. Always prefer `pnpm supabase:full-reset`. If this command fails, try to fix migration files instead of using `supabase db reset --local`.

## Running Tests

Do not run test commands in watch mode.

### All Tests

```bash
pnpm test supabase/__tests__
```

### Individual File

```bash
pnpm test supabase/__tests__/app_access.role.test.ts
```
