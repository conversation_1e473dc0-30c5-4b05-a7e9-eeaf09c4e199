/**
 * Supabase Realtime Utilities
 *
 * Provides robust utilities for handling Supabase realtime channels with
 * cold start resilience, retry logic, and health checks.
 */

import {
  RealtimeChannel,
  SupabaseClient,
  RealtimeChannelOptions
} from "@supabase/supabase-js";

export interface RealtimeConnectionOptions {
  /** Maximum number of connection attempts */
  maxRetries?: number;
  /** Base delay between retries in milliseconds */
  baseDelay?: number;
  /** Maximum delay between retries in milliseconds */
  maxDelay?: number;
  /** Timeout for individual connection attempts in milliseconds */
  connectionTimeout?: number;
}

export interface HealthCheckOptions {
  /** Maximum number of health check attempts */
  maxAttempts?: number;
  /** Delay between health check attempts in milliseconds */
  checkDelay?: number;
  /** Timeout for the entire health check process in milliseconds */
  totalTimeout?: number;
}

export interface CreateRealtimeChannelConfig {
  /** Supabase client instance */
  client: SupabaseClient;
  /** Channel name (e.g., "chat:conversation-id") */
  channelName: string;
  /** Realtime channel configuration */
  channelConfig: RealtimeChannelOptions;
  /** Optional callback to set up event listeners before health check runs */
  setupListeners?: (channel: RealtimeChannel) => Promise<void> | void;
  /** Optional health check configuration */
  healthCheck?: {
    healthCheckFn: (channel: RealtimeChannel) => Promise<void>;
    validateFn: (channel: RealtimeChannel) => boolean;
    options?: HealthCheckOptions;
  };
  /** Connection retry options */
  connectionOptions?: RealtimeConnectionOptions;
}

const DEFAULT_CONNECTION_OPTIONS: Required<RealtimeConnectionOptions> = {
  maxRetries: 3,
  baseDelay: 2000,
  maxDelay: 8000,
  connectionTimeout: 15000
};

const DEFAULT_HEALTH_CHECK_OPTIONS: Required<HealthCheckOptions> = {
  maxAttempts: 30,
  checkDelay: 500,
  totalTimeout: 30000
};

/**
 * Determines if a CHANNEL_ERROR should be retried based on context
 */
function shouldRetryChannelError(
  error: Error,
  channelConfig: RealtimeChannelOptions
): boolean {
  const errorMessage = error.message.toLowerCase();

  // For private channels, CHANNEL_ERROR is most likely an RLS/authorization issue
  // These are permanent failures and should not be retried
  if (channelConfig.config?.private === true) {
    console.log(
      "CHANNEL_ERROR on private channel - likely authorization failure, not retrying"
    );
    return false;
  }

  // Check for network/connectivity related errors that should be retried
  const networkErrorKeywords = [
    "timeout",
    "network",
    "connection",
    "connect",
    "socket",
    "websocket",
    "disconnect"
  ];

  const hasNetworkError = networkErrorKeywords.some((keyword) =>
    errorMessage.includes(keyword)
  );

  if (hasNetworkError) {
    console.log("CHANNEL_ERROR appears to be network-related - will retry");
    return true;
  }

  // For public channels or unknown error types, default to retrying
  // This is safer as it maintains resilience for transient issues
  console.log("CHANNEL_ERROR on public channel or unknown type - will retry");
  return true;
}

/**
 * Creates a realtime channel with retry logic and cold start resilience
 */
async function createChannelWithRetry(
  client: SupabaseClient,
  channelName: string,
  channelConfig: RealtimeChannelOptions,
  options: RealtimeConnectionOptions = {}
): Promise<RealtimeChannel> {
  const opts = { ...DEFAULT_CONNECTION_OPTIONS, ...options };

  await client.realtime.setAuth();

  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= opts.maxRetries; attempt++) {
    try {
      const channel = await createChannelWithTimeout(
        client,
        channelName,
        channelConfig,
        opts.connectionTimeout
      );

      console.log(
        `Realtime channel "${channelName}" connected successfully on attempt ${attempt}`
      );
      return channel;
    } catch (error) {
      lastError = error as Error;
      console.warn(
        `Realtime channel "${channelName}" connection attempt ${attempt} failed:`,
        error
      );

      // Use context-aware error handling for CHANNEL_ERROR
      if (lastError.message.includes("CHANNEL_ERROR")) {
        if (!shouldRetryChannelError(lastError, channelConfig)) {
          throw lastError;
        }
      }

      if (attempt < opts.maxRetries) {
        const delay = calculateDelay(attempt, opts);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  throw new Error(
    `Failed to connect to realtime channel "${channelName}" after ${opts.maxRetries} attempts. Last error: ${lastError?.message}`
  );
}

/**
 * Creates a channel with a timeout
 */
function createChannelWithTimeout(
  client: SupabaseClient,
  channelName: string,
  channelConfig: RealtimeChannelOptions,
  timeout: number
): Promise<RealtimeChannel> {
  return new Promise((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error(`Channel connection timeout after ${timeout}ms`));
    }, timeout);

    const channel = client
      .channel(channelName, channelConfig)
      .subscribe(async (status) => {
        console.log(`Channel "${channelName}" status:`, status);

        if (status === "SUBSCRIBED") {
          clearTimeout(timeoutId);
          resolve(channel);
        } else if (status === "CHANNEL_ERROR") {
          clearTimeout(timeoutId);
          reject(new Error(`Channel subscription failed with CHANNEL_ERROR`));
        }
      });
  });
}

/**
 * Calculates delay for retry attempts using exponential backoff
 */
function calculateDelay(
  attempt: number,
  options: Required<RealtimeConnectionOptions>
): number {
  const exponentialDelay = options.baseDelay * Math.pow(2, attempt - 1);
  return Math.min(exponentialDelay, options.maxDelay);
}

async function performHealthCheck(
  channel: RealtimeChannel,
  healthCheckFn: (channel: RealtimeChannel) => Promise<void>,
  validateFn: (channel: RealtimeChannel) => boolean,
  options: HealthCheckOptions = {}
): Promise<void> {
  const opts = { ...DEFAULT_HEALTH_CHECK_OPTIONS, ...options };
  const startTime = Date.now();

  for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
    if (Date.now() - startTime > opts.totalTimeout) {
      throw new Error(`Health check timeout after ${opts.totalTimeout}ms`);
    }

    try {
      await healthCheckFn(channel);
      await new Promise((resolve) => setTimeout(resolve, opts.checkDelay));

      if (validateFn(channel)) {
        console.log(`Health check passed on attempt ${attempt}`);
        return;
      }
    } catch (error) {
      console.warn(`Health check attempt ${attempt} failed:`, error);
    }
  }

  throw new Error(`Health check failed after ${opts.maxAttempts} attempts`);
}

/**
 * Creates a realtime channel with built-in health checking
 */
export async function createRealtimeChannel(
  config: CreateRealtimeChannelConfig
): Promise<RealtimeChannel> {
  const {
    client,
    channelName,
    channelConfig,
    setupListeners,
    healthCheck,
    connectionOptions = {}
  } = config;

  const channel = await createChannelWithRetry(
    client,
    channelName,
    channelConfig,
    connectionOptions
  );

  // Wait for initial connection stabilization
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Set up event listeners before health check if provided
  if (setupListeners) {
    await setupListeners(channel);
    // Give listeners time to be ready
    await new Promise((resolve) => setTimeout(resolve, 500));
  }

  if (healthCheck) {
    await performHealthCheck(
      channel,
      healthCheck.healthCheckFn,
      healthCheck.validateFn,
      healthCheck.options
    );
  }

  return channel;
}
