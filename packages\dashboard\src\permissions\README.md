# Type-Safe Permissions System

This document explains how to use the type-safe permissions system in the React Admin dashboard.

## Overview

The permissions system uses Zod schemas to provide runtime type safety for user roles and capabilities. Roles and capabilities are dynamically loaded from the database via the `user_roles_and_capabilities` view, ensuring they stay in sync with your database configuration.

## Schemas

### UserData

```typescript
type UserData = {
  id: string;
  email: string;
  roles: string[];
  capabilities: string[];
};
```

### Dynamic Permissions

- **Roles**: Loaded dynamically from database (e.g., "admin", "moderator", "provider", "customer", "support")
- **Capabilities**: Loaded dynamically from database (e.g., "user:read", "order:write", "ticket:assign", etc.)

## Usage

### 1. Using the usePermissions Hook

```typescript
import { usePermissions } from "../hooks/usePermissions";

function MyComponent() {
  const {
    permissions,
    isLoading,
    hasRole,
    hasCapability,
    isAdmin,
    canRead,
    canWrite,
    canDelete,
  } = usePermissions();

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      {hasRole("admin") && <AdminPanel />}
      {hasCapability("user:write") && <EditUserButton />}
      {isAdmin() && <SystemSettings />}
      {canRead("order") && <OrdersList />}
    </div>
  );
}
```

### 2. Using the PermissionGuard Component

```typescript
import { PermissionGuard } from "../components/PermissionGuard";

// Role-based access (dynamic roles from database)
<PermissionGuard role="admin">
  <AdminOnlyContent />
</PermissionGuard>

// Multiple roles (any)
<PermissionGuard roles={["admin", "moderator"]}>
  <ModeratorContent />
</PermissionGuard>

// Multiple roles (all required)
<PermissionGuard roles={["admin", "moderator"]} requireAllRoles>
  <SuperUserContent />
</PermissionGuard>

// Capability-based access (dynamic capabilities from database)
<PermissionGuard capability="user:write">
  <EditUserForm />
</PermissionGuard>

// Multiple capabilities (any)
<PermissionGuard capabilities={["user:read", "user:write"]}>
  <UserManagement />
</PermissionGuard>

// Resource-based access
<PermissionGuard resource="order" action="delete">
  <DeleteOrderButton />
</PermissionGuard>

// Admin-only access
<PermissionGuard adminOnly>
  <SystemAdminPanel />
</PermissionGuard>

// With fallback content
<PermissionGuard
  capability="user:write"
  fallback={<ReadOnlyUserView />}
>
  <EditableUserView />
</PermissionGuard>
```

### 3. Higher-Order Component

```typescript
import { withPermissions } from "../components/PermissionGuard";

const AdminOnlyComponent = withPermissions(MyComponent, { adminOnly: true });
const UserWriteComponent = withPermissions(MyComponent, {
  capability: "user:write",
});
```

## React Admin Integration

### Resource-level Permissions

```typescript
// In App.tsx
<PermissionGuard capability="user:read">
  <Resource
    name="users"
    list={UserList}
    edit={
      <PermissionGuard capability="user:write" fallback={<UserShow />}>
        <UserEdit />
      </PermissionGuard>
    }
    create={
      <PermissionGuard capability="user:write">
        <UserCreate />
      </PermissionGuard>
    }
  />
</PermissionGuard>
```

### Action-level Permissions

```typescript
// In a list component
import { usePermissions } from "../hooks/usePermissions";

function UserList() {
  const { canWrite, canDelete } = usePermissions();

  return (
    <List>
      <Datagrid>
        <TextField source="name" />
        <TextField source="email" />
        {canWrite("user") && <EditButton />}
        {canDelete("user") && <DeleteButton />}
      </Datagrid>
    </List>
  );
}
```

## Type Safety Benefits

1. **Compile-time validation**: TypeScript will catch invalid role/capability references
2. **IntelliSense support**: Auto-completion for valid permissions
3. **Refactoring safety**: Renaming permissions will update all references
4. **Runtime validation**: Zod schemas validate data from the server

## Adding New Permissions

To add new roles or capabilities:

1. **Add to Database**: Insert new roles/capabilities into your database tables
2. **Database View**: The `user_roles_and_capabilities` view will automatically include them
3. **No Code Changes**: The system dynamically loads permissions from the database
4. **Use Immediately**: New permissions are available in components without code changes

Example database operations:

```sql
-- Add new role
INSERT INTO app_access.role (name, description) VALUES ('new-role', 'Description');

-- Add new capability
INSERT INTO app_access.capability (name, description) VALUES ('new-resource:read', 'Description');

-- Assign to users via role_capability and user_role tables
```

The permissions system automatically stays in sync with your database configuration.

## Testing

The `PermissionDemo` component (`/permissions-demo` resource) shows examples of all permission patterns and can be used for testing different user permission combinations.
