# Type-Safe Permissions System

This document explains how to use the type-safe permissions system in the React Admin dashboard.

## Overview

The permissions system uses Zod schemas to provide compile-time type safety for user roles and capabilities. This ensures that you can only reference valid permissions in your code and catch errors early.

## Schemas

### UserRole
```typescript
type UserRole = "admin" | "moderator" | "provider" | "customer" | "support"
```

### UserCapability
```typescript
type UserCapability = 
  | "user:read" | "user:write" | "user:delete"
  | "order:read" | "order:write" | "order:delete"
  | "ticket:read" | "ticket:write" | "ticket:assign"
  | "provider:read" | "provider:write" | "provider:approve"
  | "activity:read" | "activity:write"
  | "system:admin"
```

## Usage

### 1. Using the usePermissions Hook

```typescript
import { usePermissions } from "../hooks/usePermissions";

function MyComponent() {
  const {
    permissions,
    isLoading,
    hasRole,
    hasCapability,
    isAdmin,
    canRead,
    canWrite,
    canDelete,
  } = usePermissions();

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      {hasRole("admin") && <AdminPanel />}
      {hasCapability("user:write") && <EditUserButton />}
      {isAdmin() && <SystemSettings />}
      {canRead("order") && <OrdersList />}
    </div>
  );
}
```

### 2. Using the PermissionGuard Component

```typescript
import { PermissionGuard } from "../components/PermissionGuard";

// Role-based access
<PermissionGuard role="admin">
  <AdminOnlyContent />
</PermissionGuard>

// Multiple roles (any)
<PermissionGuard roles={["admin", "moderator"]}>
  <ModeratorContent />
</PermissionGuard>

// Multiple roles (all required)
<PermissionGuard roles={["admin", "moderator"]} requireAllRoles>
  <SuperUserContent />
</PermissionGuard>

// Capability-based access
<PermissionGuard capability="user:write">
  <EditUserForm />
</PermissionGuard>

// Multiple capabilities (any)
<PermissionGuard capabilities={["user:read", "user:write"]}>
  <UserManagement />
</PermissionGuard>

// Resource-based access
<PermissionGuard resource="order" action="delete">
  <DeleteOrderButton />
</PermissionGuard>

// Admin-only access
<PermissionGuard adminOnly>
  <SystemAdminPanel />
</PermissionGuard>

// With fallback content
<PermissionGuard 
  capability="user:write" 
  fallback={<ReadOnlyUserView />}
>
  <EditableUserView />
</PermissionGuard>
```

### 3. Higher-Order Component

```typescript
import { withPermissions } from "../components/PermissionGuard";

const AdminOnlyComponent = withPermissions(MyComponent, { adminOnly: true });
const UserWriteComponent = withPermissions(MyComponent, { capability: "user:write" });
```

## React Admin Integration

### Resource-level Permissions

```typescript
// In App.tsx
<PermissionGuard capability="user:read">
  <Resource 
    name="users" 
    list={UserList}
    edit={
      <PermissionGuard capability="user:write" fallback={<UserShow />}>
        <UserEdit />
      </PermissionGuard>
    }
    create={
      <PermissionGuard capability="user:write">
        <UserCreate />
      </PermissionGuard>
    }
  />
</PermissionGuard>
```

### Action-level Permissions

```typescript
// In a list component
import { usePermissions } from "../hooks/usePermissions";

function UserList() {
  const { canWrite, canDelete } = usePermissions();

  return (
    <List>
      <Datagrid>
        <TextField source="name" />
        <TextField source="email" />
        {canWrite("user") && <EditButton />}
        {canDelete("user") && <DeleteButton />}
      </Datagrid>
    </List>
  );
}
```

## Type Safety Benefits

1. **Compile-time validation**: TypeScript will catch invalid role/capability references
2. **IntelliSense support**: Auto-completion for valid permissions
3. **Refactoring safety**: Renaming permissions will update all references
4. **Runtime validation**: Zod schemas validate data from the server

## Adding New Permissions

To add new roles or capabilities:

1. Update the schemas in `providers/authProvider.ts`:
```typescript
export const UserRoleSchema = z.enum([
  "admin",
  "moderator", 
  "provider",
  "customer",
  "support",
  "new-role", // Add here
]);

export const UserCapabilitySchema = z.enum([
  // ... existing capabilities
  "new-resource:read",
  "new-resource:write",
  "new-resource:delete",
]);
```

2. TypeScript will automatically infer the new types
3. Use the new permissions in your components

## Testing

The `PermissionDemo` component (`/permissions-demo` resource) shows examples of all permission patterns and can be used for testing different user permission combinations.
