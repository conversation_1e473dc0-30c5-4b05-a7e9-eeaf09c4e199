import {
  type NotificationType,
  validateNotificationData,
  validateNotification
} from "lib/schemas/notification";
import type { Database } from "lib/supabase/database";

export type NotificationRow =
  Database["app_account"]["Tables"]["notification"]["Row"];

// Helper function to validate and find notifications
export function findValidatedNotification<T extends NotificationType>(
  notifications: NotificationRow[],
  type: T,
  predicate?: (data: ReturnType<typeof validateNotificationData<T>>) => boolean
) {
  const notification = notifications.find((n) => n.type === type);
  if (!notification) return null;

  // Validate the notification structure
  const validatedNotification = validateNotification(notification);

  // Validate the notification data
  const validatedData = validateNotificationData(
    type,
    validatedNotification.data
  );

  // Check predicate if provided
  if (predicate && !predicate(validatedData)) return null;

  return { notification: validatedNotification, data: validatedData };
}
