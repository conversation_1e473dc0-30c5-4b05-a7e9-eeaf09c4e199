import React from "react";
import { usePermissions } from "../hooks/usePermissions";
import type { UserRole, UserCapability } from "../providers/authProvider";

interface PermissionGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  
  // Role-based access
  role?: UserRole;
  roles?: UserRole[];
  requireAllRoles?: boolean; // If true, requires all roles; if false, requires any role
  
  // Capability-based access
  capability?: UserCapability;
  capabilities?: UserCapability[];
  requireAllCapabilities?: boolean; // If true, requires all capabilities; if false, requires any capability
  
  // Admin check
  adminOnly?: boolean;
  
  // Resource-based access
  resource?: string;
  action?: "read" | "write" | "delete";
}

/**
 * Component that conditionally renders children based on user permissions
 */
export function PermissionGuard({
  children,
  fallback = null,
  role,
  roles,
  requireAllRoles = false,
  capability,
  capabilities,
  requireAllCapabilities = false,
  adminOnly = false,
  resource,
  action,
}: PermissionGuardProps) {
  const {
    hasRole,
    hasAnyRole,
    hasAllRoles,
    hasCapability,
    hasAnyCapability,
    hasAllCapabilities,
    isAdmin,
    canRead,
    canWrite,
    canDelete,
    isLoading,
  } = usePermissions();

  // Show loading state if permissions are still loading
  if (isLoading) {
    return <>{fallback}</>;
  }

  // Check admin access
  if (adminOnly && !isAdmin()) {
    return <>{fallback}</>;
  }

  // Check single role
  if (role && !hasRole(role)) {
    return <>{fallback}</>;
  }

  // Check multiple roles
  if (roles && roles.length > 0) {
    const hasAccess = requireAllRoles ? hasAllRoles(roles) : hasAnyRole(roles);
    if (!hasAccess) {
      return <>{fallback}</>;
    }
  }

  // Check single capability
  if (capability && !hasCapability(capability)) {
    return <>{fallback}</>;
  }

  // Check multiple capabilities
  if (capabilities && capabilities.length > 0) {
    const hasAccess = requireAllCapabilities 
      ? hasAllCapabilities(capabilities) 
      : hasAnyCapability(capabilities);
    if (!hasAccess) {
      return <>{fallback}</>;
    }
  }

  // Check resource-based access
  if (resource && action) {
    let hasAccess = false;
    switch (action) {
      case "read":
        hasAccess = canRead(resource);
        break;
      case "write":
        hasAccess = canWrite(resource);
        break;
      case "delete":
        hasAccess = canDelete(resource);
        break;
    }
    if (!hasAccess) {
      return <>{fallback}</>;
    }
  }

  // If all checks pass, render children
  return <>{children}</>;
}

/**
 * Higher-order component version of PermissionGuard
 */
export function withPermissions<P extends object>(
  Component: React.ComponentType<P>,
  permissionProps: Omit<PermissionGuardProps, "children" | "fallback">
) {
  return function PermissionWrappedComponent(props: P) {
    return (
      <PermissionGuard {...permissionProps}>
        <Component {...props} />
      </PermissionGuard>
    );
  };
}
