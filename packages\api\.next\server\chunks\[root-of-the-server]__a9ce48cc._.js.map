{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Github/helalsoft/esenpai/lib/utils/result.ts"], "sourcesContent": ["export function ok<T>(data?: T) {\r\n  return {\r\n    success: true as const,\r\n    data\r\n  };\r\n}\r\n\r\nexport function err<const T>(type: T, message: string) {\r\n  return {\r\n    success: false as const,\r\n    error: {\r\n      type,\r\n      message\r\n    }\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;AAAO,SAAS,GAAM,IAAQ;IAC5B,OAAO;QACL,SAAS;QACT;IACF;AACF;AAEO,SAAS,IAAa,IAAO,EAAE,OAAe;IACnD,OAAO;QACL,SAAS;QACT,OAAO;YACL;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Github/helalsoft/esenpai/lib/utils/trycatch.ts"], "sourcesContent": ["export async function tryCatch<T, E = Error>(promise: T | Promise<T>) {\r\n  try {\r\n    const data = await promise;\r\n    return [null, data] as const;\r\n  } catch (error) {\r\n    return [error as E, null] as const;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,eAAe,SAAuB,OAAuB;IAClE,IAAI;QACF,MAAM,OAAO,MAAM;QACnB,OAAO;YAAC;YAAM;SAAK;IACrB,EAAE,OAAO,OAAO;QACd,OAAO;YAAC;YAAY;SAAK;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Github/helalsoft/esenpai/lib/supabase/service-client.ts"], "sourcesContent": ["import { Database } from \"./database\";\nimport { createClient as createSupabaseClient } from \"@supabase/supabase-js\";\nimport { load } from \"dotenv-mono\";\n\n// Load environment variables\nload();\n\n/**\n * Service client for administrative operations\n * Uses the service role key to bypass RLS policies\n */\nexport function createServiceClient() {\n  return createSupabaseClient<Database>(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\n    {\n      auth: {\n        autoRefreshToken: false,\n        persistSession: false\n      }\n    }\n  );\n}\n\n// Export a singleton instance for convenience\nexport const serviceClient = createServiceClient();\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAEA,6BAA6B;AAC7B,CAAA,GAAA,6MAAA,CAAA,OAAI,AAAD;AAMI,SAAS;IACd,OAAO,CAAA,GAAA,mQAAA,CAAA,eAAoB,AAAD,8DAExB,QAAQ,GAAG,CAAC,yBAAyB,EACrC;QACE,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AAEJ;AAGO,MAAM,gBAAgB", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Github/helalsoft/esenpai/packages/api/lib/index.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\r\nimport { err } from \"lib/utils/result\";\r\n\r\nexport async function verifyApiCallSecret(\r\n  request: Request,\r\n  logId: string\r\n): Promise<NextResponse<ReturnType<typeof err>> | null> {\r\n  const apiCallSecret = request.headers.get(\"X-API-CALL-SECRET\");\r\n  if (apiCallSecret !== process.env.API_CALL_SECRET) {\r\n    console.error(`[${logId}] Invalid API call secret.`);\r\n    return NextResponse.json(err(\"NotFound\", \"Route Not Found\"), {\r\n      status: 404\r\n    });\r\n  }\r\n  return null;\r\n}\r\n\r\nexport function generateLogId(): string {\r\n  return Date.now().toString() + Math.random().toString(36).substring(2, 15);\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,oBACpB,OAAgB,EAChB,KAAa;IAEb,MAAM,gBAAgB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC1C,IAAI,kBAAkB,QAAQ,GAAG,CAAC,eAAe,EAAE;QACjD,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,0BAA0B,CAAC;QACnD,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,GAAA,wHAAA,CAAA,MAAG,AAAD,EAAE,YAAY,oBAAoB;YAC3D,QAAQ;QACV;IACF;IACA,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AACzE", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Github/helalsoft/esenpai/packages/api/app/base64/route.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\r\nimport { err, ok } from \"lib/utils/result\";\r\nimport { tryCatch } from \"lib/utils/trycatch\";\r\nimport sharp from \"sharp\";\r\nimport { createServiceClient } from \"lib/supabase/service-client\";\r\nimport { z } from \"zod\";\r\nimport { generateLogId, verifyApiCallSecret } from \"@/lib\";\r\n\r\n// Define schema for incoming request body\r\nconst requestBodySchema = z.object({\r\n  object_id: z.string(),\r\n  bucket_id: z.string(),\r\n  name: z.string()\r\n});\r\n\r\n// Handle POST requests to process and update image base64\r\nexport async function POST(request: Request) {\r\n  const logId = generateLogId();\r\n\r\n  console.log(`[${logId}] Received POST request to ${request.url}`);\r\n\r\n  const authError = await verifyApiCallSecret(request, logId);\r\n  if (authError) {\r\n    return authError;\r\n  }\r\n\r\n  const supabase = createServiceClient();\r\n\r\n  // Parse and validate request body\r\n  const [jsonError, requestBody] = await tryCatch(request.json());\r\n\r\n  if (jsonError) {\r\n    console.error(`[${logId}] Invalid request body:`, jsonError);\r\n    return NextResponse.json(err(\"InvalidRequest\", \"Invalid request body\"));\r\n  }\r\n\r\n  console.log(\r\n    `[${logId}] Request body parsed successfully. Received input:`,\r\n    requestBody\r\n  );\r\n  const validation = requestBodySchema.safeParse(requestBody);\r\n\r\n  if (!validation.success) {\r\n    console.error(\r\n      `[${logId}] Invalid input validation error:`,\r\n      validation.error.message\r\n    );\r\n    return NextResponse.json(err(\"InvalidInput\", validation.error.message));\r\n  }\r\n\r\n  console.log(\r\n    `[${logId}] Input validated. Data: bucket_id=${validation.data.bucket_id}, name=${validation.data.name}`\r\n  );\r\n  const { bucket_id, name } = validation.data;\r\n\r\n  // Download image from Supabase storage\r\n  console.log(\r\n    `[${logId}] Attempting to download file from bucket: ${bucket_id}, name: ${name}`\r\n  );\r\n  const download = await supabase.storage.from(bucket_id).download(name);\r\n\r\n  if (download.error) {\r\n    console.error(`[${logId}] File download error:`, download.error.message);\r\n    return NextResponse.json(err(\"FileDownloadError\", download.error.message));\r\n  }\r\n\r\n  if (!download.data) {\r\n    console.error(`[${logId}] File not found after download attempt.`);\r\n    return NextResponse.json(err(\"FileNotFound\", \"File not found\"));\r\n  }\r\n\r\n  // Process image: resize and convert to WebP\r\n  console.log(`[${logId}] File downloaded successfully. Processing image.`);\r\n  const arrayBuffer = await download.data.arrayBuffer();\r\n  const imageBuffer = Buffer.from(arrayBuffer);\r\n\r\n  const processedImageBuffer = await sharp(imageBuffer)\r\n    .resize({ width: 20, withoutEnlargement: true, fit: \"inside\" })\r\n    .webp()\r\n    .toBuffer();\r\n\r\n  // Convert processed image buffer to base64\r\n  const base64 = processedImageBuffer.toString(\"base64\");\r\n  console.log(`[${logId}] Image processed and converted to base64.`);\r\n\r\n  // Update database with the new base64 string\r\n  console.log(\r\n    `[${logId}] Attempting to update database for object_id: ${validation.data.object_id}`\r\n  );\r\n  const updating = await supabase\r\n    .schema(\"app_media\")\r\n    .from(\"image\")\r\n    .update({ base64_placeholder: base64 })\r\n    .eq(\"object_id\", validation.data.object_id);\r\n\r\n  if (updating.error) {\r\n    console.error(`[${logId}] Database update error:`, updating.error.message);\r\n    return NextResponse.json(err(\"DatabaseError\", updating.error.message));\r\n  }\r\n\r\n  // Return success\r\n  console.log(`[${logId}] Database updated successfully. Process completed.`);\r\n  return NextResponse.json(ok());\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;AAEA,0CAA0C;AAC1C,MAAM,oBAAoB,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,WAAW,qOAAA,CAAA,IAAC,CAAC,MAAM;IACnB,WAAW,qOAAA,CAAA,IAAC,CAAC,MAAM;IACnB,MAAM,qOAAA,CAAA,IAAC,CAAC,MAAM;AAChB;AAGO,eAAe,KAAK,OAAgB;IACzC,MAAM,QAAQ,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD;IAE1B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,2BAA2B,EAAE,QAAQ,GAAG,EAAE;IAEhE,MAAM,YAAY,MAAM,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS;IACrD,IAAI,WAAW;QACb,OAAO;IACT;IAEA,MAAM,WAAW,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD;IAEnC,kCAAkC;IAClC,MAAM,CAAC,WAAW,YAAY,GAAG,MAAM,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI;IAE5D,IAAI,WAAW;QACb,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,uBAAuB,CAAC,EAAE;QAClD,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,GAAA,wHAAA,CAAA,MAAG,AAAD,EAAE,kBAAkB;IACjD;IAEA,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,MAAM,mDAAmD,CAAC,EAC9D;IAEF,MAAM,aAAa,kBAAkB,SAAS,CAAC;IAE/C,IAAI,CAAC,WAAW,OAAO,EAAE;QACvB,QAAQ,KAAK,CACX,CAAC,CAAC,EAAE,MAAM,iCAAiC,CAAC,EAC5C,WAAW,KAAK,CAAC,OAAO;QAE1B,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,GAAA,wHAAA,CAAA,MAAG,AAAD,EAAE,gBAAgB,WAAW,KAAK,CAAC,OAAO;IACvE;IAEA,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,MAAM,mCAAmC,EAAE,WAAW,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE;IAE1G,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,WAAW,IAAI;IAE3C,uCAAuC;IACvC,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,MAAM,2CAA2C,EAAE,UAAU,QAAQ,EAAE,MAAM;IAEnF,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;IAEjE,IAAI,SAAS,KAAK,EAAE;QAClB,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,sBAAsB,CAAC,EAAE,SAAS,KAAK,CAAC,OAAO;QACvE,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,GAAA,wHAAA,CAAA,MAAG,AAAD,EAAE,qBAAqB,SAAS,KAAK,CAAC,OAAO;IAC1E;IAEA,IAAI,CAAC,SAAS,IAAI,EAAE;QAClB,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,wCAAwC,CAAC;QACjE,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,GAAA,wHAAA,CAAA,MAAG,AAAD,EAAE,gBAAgB;IAC/C;IAEA,4CAA4C;IAC5C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,iDAAiD,CAAC;IACxE,MAAM,cAAc,MAAM,SAAS,IAAI,CAAC,WAAW;IACnD,MAAM,cAAc,OAAO,IAAI,CAAC;IAEhC,MAAM,uBAAuB,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,aACtC,MAAM,CAAC;QAAE,OAAO;QAAI,oBAAoB;QAAM,KAAK;IAAS,GAC5D,IAAI,GACJ,QAAQ;IAEX,2CAA2C;IAC3C,MAAM,SAAS,qBAAqB,QAAQ,CAAC;IAC7C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,0CAA0C,CAAC;IAEjE,6CAA6C;IAC7C,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,MAAM,+CAA+C,EAAE,WAAW,IAAI,CAAC,SAAS,EAAE;IAExF,MAAM,WAAW,MAAM,SACpB,MAAM,CAAC,aACP,IAAI,CAAC,SACL,MAAM,CAAC;QAAE,oBAAoB;IAAO,GACpC,EAAE,CAAC,aAAa,WAAW,IAAI,CAAC,SAAS;IAE5C,IAAI,SAAS,KAAK,EAAE;QAClB,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,wBAAwB,CAAC,EAAE,SAAS,KAAK,CAAC,OAAO;QACzE,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,GAAA,wHAAA,CAAA,MAAG,AAAD,EAAE,iBAAiB,SAAS,KAAK,CAAC,OAAO;IACtE;IAEA,iBAAiB;IACjB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,mDAAmD,CAAC;IAC1E,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD;AAC5B", "debugId": null}}]}