import {
  Ad<PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  SimpleForm,
  TextInput,
} from "react-admin";
import { Layout } from "./Layout";
import { lightTheme, darkTheme } from "./theme";
import { authProvider } from "./providers/authProvider";
import { dataProvider } from "./providers/dataProvider";
import { LoginPage } from "./components/LoginPage";

// Simple create component for testing
const SimpleCreate = () => (
  <Create>
    <SimpleForm>
      <TextInput source="name" />
      <TextInput source="description" />
    </SimpleForm>
  </Create>
);

export const App = () => (
  <Admin
    layout={Layout}
    theme={lightTheme}
    darkTheme={darkTheme}
    authProvider={authProvider}
    dataProvider={dataProvider}
    loginPage={LoginPage}
  >
    <Resource name="test" list={ListGuesser} />

    <Resource
      name="users"
      list={ListGuesser}
      show={ShowGuesser}
      edit={EditGuesser}
      create={SimpleCreate}
    />

    <Resource
      name="orders"
      list={ListGuesser}
      show={ShowGuesser}
      edit={EditGuesser}
      create={SimpleCreate}
    />

    <Resource
      name="tickets"
      list={ListGuesser}
      show={ShowGuesser}
      edit={EditGuesser}
      create={SimpleCreate}
    />

    <Resource
      name="providers"
      list={ListGuesser}
      show={ShowGuesser}
      edit={EditGuesser}
      create={SimpleCreate}
    />

    <Resource
      name="activities"
      list={ListGuesser}
      show={ShowGuesser}
      edit={EditGuesser}
      create={SimpleCreate}
    />
  </Admin>
);
