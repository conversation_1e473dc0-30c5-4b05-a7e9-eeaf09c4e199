import {
  Ad<PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  SimpleForm,
  TextInput,
} from "react-admin";
import { Layout } from "./Layout";
import { lightTheme, darkTheme } from "./theme";
import { authProvider } from "./providers/authProvider";
import { dataProvider } from "./providers/dataProvider";
import { LoginPage } from "./components/LoginPage";
import { PermissionGuard } from "./components/PermissionGuard";
import { PermissionDemo } from "./components/PermissionDemo";

// Simple create component for testing
const SimpleCreate = () => (
  <Create>
    <SimpleForm>
      <TextInput source="name" />
      <TextInput source="description" />
    </SimpleForm>
  </Create>
);

export const App = () => (
  <Admin
    layout={Layout}
    theme={lightTheme}
    darkTheme={darkTheme}
    authProvider={authProvider}
    dataProvider={dataProvider}
    loginPage={LoginPage}
  >
    {/* Test resource - visible to all authenticated users */}
    <Resource name="test" list={<PERSON><PERSON><PERSON>ser} />

    {/* Permission Demo - visible to all authenticated users */}
    <Resource name="permissions-demo" list={PermissionDemo} />

    {/* User management - admin only */}
    <PermissionGuard adminOnly>
      <Resource
        name="users"
        list={ListGuesser}
        show={ShowGuesser}
        edit={EditGuesser}
        create={SimpleCreate}
      />
    </PermissionGuard>

    {/* Orders - requires order:read capability */}
    <PermissionGuard capability="order:read">
      <Resource
        name="orders"
        list={ListGuesser}
        show={ShowGuesser}
        edit={
          <PermissionGuard capability="order:write" fallback={<ShowGuesser />}>
            <EditGuesser />
          </PermissionGuard>
        }
        create={
          <PermissionGuard capability="order:write">
            <SimpleCreate />
          </PermissionGuard>
        }
      />
    </PermissionGuard>

    {/* Tickets - requires ticket:read capability */}
    <PermissionGuard capability="ticket:read">
      <Resource
        name="tickets"
        list={ListGuesser}
        show={ShowGuesser}
        edit={
          <PermissionGuard capability="ticket:write" fallback={<ShowGuesser />}>
            <EditGuesser />
          </PermissionGuard>
        }
        create={
          <PermissionGuard capability="ticket:write">
            <SimpleCreate />
          </PermissionGuard>
        }
      />
    </PermissionGuard>

    {/* Providers - requires provider role or provider:read capability */}
    <PermissionGuard
      roles={["admin", "moderator"]}
      capabilities={["provider:read"]}
    >
      <Resource
        name="providers"
        list={ListGuesser}
        show={ShowGuesser}
        edit={
          <PermissionGuard
            capability="provider:write"
            fallback={<ShowGuesser />}
          >
            <EditGuesser />
          </PermissionGuard>
        }
        create={
          <PermissionGuard capability="provider:write">
            <SimpleCreate />
          </PermissionGuard>
        }
      />
    </PermissionGuard>

    {/* Activities - requires activity:read capability */}
    <PermissionGuard capability="activity:read">
      <Resource
        name="activities"
        list={ListGuesser}
        show={ShowGuesser}
        edit={
          <PermissionGuard
            capability="activity:write"
            fallback={<ShowGuesser />}
          >
            <EditGuesser />
          </PermissionGuard>
        }
        create={
          <PermissionGuard capability="activity:write">
            <SimpleCreate />
          </PermissionGuard>
        }
      />
    </PermissionGuard>
  </Admin>
);
